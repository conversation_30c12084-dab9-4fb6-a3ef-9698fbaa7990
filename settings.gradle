rootProject.name = 'spark-yun'

include 'spark-yun-agent'
include 'spark-yun-dist'
include 'spark-yun-frontend'
include 'spark-yun-backend'
include 'spark-yun-backend:spark-yun-main'
include 'spark-yun-backend:spark-yun-api-base'
include 'spark-yun-backend:spark-yun-api'
include 'spark-yun-backend:spark-yun-common'
include 'spark-yun-backend:spark-yun-modules'
include 'spark-yun-backend:spark-yun-security'
include 'spark-yun-plugins'
include 'spark-yun-plugins:spark-query-sql-plugin'
include 'spark-yun-plugins:spark-data-sync-jdbc-plugin'
include 'spark-yun-plugins:spark-excel-sync-jdbc-plugin'
include 'spark-yun-vip'
include 'spark-yun-vip:spark-yun-backend'
include 'spark-yun-vip:spark-yun-license'
include 'spark-yun-vip:spark-yun-plugins'
include 'spark-yun-vip:spark-yun-plugins:spark-container-sql-plugin'
include 'spark-yun-vip:spark-yun-plugins:spark-real-sync-plugin'
include 'spark-yun-vip:spark-yun-plugins:spark-db-migrator-plugin'