tasks:
  - name: 本地启动
    init: ./gradlew install
    command: ./gradlew package
ports:
  - name: server
    port: 8080
    description: 至轻云开放访问端口
    visibility: public
  - name: agent
    port: 30177
    description: 代理开放访问端口
    visibility: public
  - name: spark-server
    port: 7077
    description: spark服务
    visibility: public
  - name: spark-ui
    port: 8081
    description: spark ui
    visibility: public

# 安装必要插件
vscode:
  extensions:
    - akamud.vscode-theme-onedark
    - vscjava.vscode-java-debug
    - vscjava.vscode-java-pack
    - eamodio.gitlens
    - redhat.java
    - GabrielBB.vscode-lombok
    - PKief.material-icon-theme
    - vscjava.vscode-java-dependency
    - vscjava.vscode-spring-boot-dashboard
    - vmware.vscode-spring-boot
