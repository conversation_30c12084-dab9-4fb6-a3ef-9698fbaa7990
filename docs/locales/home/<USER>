{"home": "Главная", "zhi_qing_yun": "Zhiqing Cloud", "enterprise_data_platform": "Легковесная платформа вычислений больших данных", "quick_start": "Быстрый старт", "experience_now": "Попробовать сейчас", "video_introduction": "Видео введение", "copy_success": "Успешно скопировано", "choose_light_cloud": "Выбрать Zhiqing Cloud", "related_technologies": "Связанные технологии", "data_drives_value": "Данные создают ценность, большие данные создают большую ценность", "data_drives_value_description": "ИИ пришел, <PERSON><PERSON><PERSON> Cloud сотрудничает с вами для реализации корпоративного центра данных", "light_cloud_description": "Zhiqing Cloud - это сверхлегкая, корпоративная платформа вычислений больших данных. Развертывание в один клик, готова к использованию. Может быстро реализовать вычисления больших данных, сбор данных, очистку данных, безопасность данных, качество данных, управление данными, открытие интерфейсов данных и другие функции, помогая предприятиям строить центр больших данных.", "light_cloud_description_mobile": "Zhiqing Cloud - это сверхлегкая, корпоративная платформа вычислений больших данных. Развертывание в один клик, готова к использованию. Может быстро реализовать вычисления больших данных, сбор данных, очистку данных, безопасность данных, качество данных, управление данных, открытие интерфейсов данных и другие функции, помогая предприятиям строить центр больших данных.", "coding_capability": "Панорамный мониторинг платформы", "job_types_supported": "Совместим с основными движками планирования ресурсов, такими ка<PERSON>, CDH, Kubernetes, Spark и т.д., реализуя единый мониторинг ресурсов и системы.", "job_orchestration": "Пользовательский плагин, не только SQL", "job_support": "Поддерживает множество типов заданий, включая пользовательские задания, задачи Spark, скрипты Python, синхронизацию данных, скрип<PERSON><PERSON>sh, импорт Excel, вызовы интерфейсов, задания SQL, скрипты Curl и т.д.", "real_work": "Гибкое планирование, перетаскивание по вертикали и горизонтали", "real_work_description": "Поддерживает все команды жизненного цикла заданий, включая выполнение, остановку, прерывание, отключение, публикацию, полное повторное выполнение, повторное выполнение нижестоящих, повторное выполнение текущего, внешний вызов, передачу результатов и т.д.", "multi_platform_deployment": "Карта метаданных, ясно с первого взгляда", "multi_platform_description": "Предоставляет функцию сбора метаданных, помогает пользователям быстро искать распределение и структуру данных, поддерживает подключение к нескольким источникам данных и многомерное визуальное отображение.", "data_view": "Умный большой экран, визуализация больших данных", "data_view_description": "Мощная технология обработки и визуализации данных, преобразует сложные данные в интуитивные диаграммы, поддерживает мониторинг и анализ в реальном времени, помогает в принятии корпоративных решений, гибко адаптируется к потребностям отрасли.", "opensource_value": "Помощь предприятиям в использовании возможностей больших данных", "free_trial": "Бесплатная пробная версия", "zhi_yao_shu_ju": "Zhiyao Data", "build_enterprise_open_source_software": "Легковесная платформа вычислений больших данных"}