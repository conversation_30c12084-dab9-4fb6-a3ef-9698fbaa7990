{"home": "홈", "zhi_qing_yun": "지경운", "enterprise_data_platform": "경량급 빅데이터 컴퓨팅 플랫폼", "quick_start": "빠른 시작", "experience_now": "지금 체험", "video_introduction": "비디오 소개", "copy_success": "복사 성공", "choose_light_cloud": "지경운 선택", "related_technologies": "관련 기술", "data_drives_value": "데이터가 가치를 창조하고, 빅데이터가 큰 가치를 창조", "data_drives_value_description": "AI 시대가 도래했습니다. 지경운과 함께 기업급 데이터센터 구축을 실현하세요", "light_cloud_description": "지경운은 초경량급, 기업급 빅데이터 컴퓨팅 플랫폼입니다. 원클릭 배포, 즉시 사용 가능. 빅데이터 컴퓨팅, 데이터 수집, 데이터 정제, 데이터 보안, 데이터 품질, 데이터 관리, 데이터 인터페이스 개방 등의 기능을 신속하게 구현하여 기업의 빅데이터센터 구축을 지원합니다.", "light_cloud_description_mobile": "지경운은 초경량급, 기업급 빅데이터 컴퓨팅 플랫폼입니다. 원클릭 배포, 즉시 사용 가능. 빅데이터 컴퓨팅, 데이터 수집, 데이터 정제, 데이터 보안, 데이터 품질, 데이터 관리, 데이터 인터페이스 개방 등의 기능을 신속하게 구현하여 기업의 빅데이터센터 구축을 지원합니다.", "coding_capability": "전경화 플랫폼 모니터링", "job_types_supported": "<PERSON><PERSON>, CDH, Kubernetes, Spark 등 주류 리소스 스케줄링 엔진과 호환되어 리소스와 시스템의 통합 모니터링을 실현합니다.", "job_orchestration": "사용자 정의 플러그인, SQL만이 아닌", "job_support": "사용자 정의 작업, Spark 태스크, Python 스크립트, 데이터 동기화, <PERSON>sh 스크립트, Excel 가져오기, 인터페이스 호출, SQL 작업, Curl 스크립트 등 다양한 작업 유형을 지원합니다.", "real_work": "유연한 스케줄링, 세로 가로 모두 드래그 가능", "real_work_description": "실행, 중지, 중단, 오프라인, 게시, 전체 재실행, 다운스트림 재실행, 현재 재실행, 외부 호출, 결과 전달 등 완전한 작업 생명주기 명령을 지원합니다.", "multi_platform_deployment": "메타데이터 맵, 한눈에 파악", "multi_platform_description": "메타데이터 수집 기능을 제공하여 사용자가 데이터의 분포와 구조를 신속하게 검색할 수 있도록 지원하며, 다양한 데이터 소스 접속과 다차원 시각화 표시를 지원합니다.", "data_view": "지능형 대형 화면, 빅데이터 시각화", "data_view_description": "강력한 데이터 처리 및 시각화 기술로 복잡한 데이터를 직관적인 차트로 변환하고, 실시간 모니터링과 분석을 지원하여 기업 의사결정을 지원하며, 업계 내 요구사항에 유연하게 적응합니다.", "opensource_value": "기업의 빅데이터 능력 활용 지원", "free_trial": "무료 체험", "zhi_yao_shu_ju": "지효데이터", "build_enterprise_open_source_software": "경량급 빅데이터 컴퓨팅 플랫폼"}