{"home": "<PERSON><PERSON>o", "zhi_qing_yun": "Zhiqing Cloud", "enterprise_data_platform": "Plataforma de computación Big Data ligera", "quick_start": "<PERSON><PERSON><PERSON>", "experience_now": "<PERSON><PERSON> ahora", "video_introduction": "Introducción en video", "copy_success": "<PERSON><PERSON> exitosa", "choose_light_cloud": "Elegir Z<PERSON> Cloud", "related_technologies": "Tecnologías relacionadas", "data_drives_value": "Los datos crean valor, Big Data crea gran valor", "data_drives_value_description": "La IA ha llegado, Zhiqing Cloud se asocia contigo para realizar un centro de datos empresarial", "light_cloud_description": "Zhiqing Cloud es una plataforma de computación Big Data ultraligera y de nivel empresarial. Implementación con un clic, listo para usar. Puede realizar rápidamente computación Big Data, recolección de datos, limpieza de datos, seguridad de datos, calidad de datos, gestión de datos, apertura de interfaces de datos y otras funciones, ayudando a las empresas a construir un centro Big Data.", "light_cloud_description_mobile": "Zhiqing Cloud es una plataforma de computación Big Data ultraligera y de nivel empresarial. Implementación con un clic, listo para usar. Puede realizar rápidamente computación Big Data, recolección de datos, limpieza de datos, seguridad de datos, calidad de datos, gestión de datos, apertura de interfaces de datos y otras funciones, ayudando a las empresas a construir un centro Big Data.", "coding_capability": "Monitoreo panorámico de plataforma", "job_types_supported": "Compatible con motores de programación de recursos principales como Hadoop, CDH, Kubernetes, Spark, etc., realizando monitoreo unificado de recursos y sistemas.", "job_orchestration": "Plugin personalizado, no solo SQL", "job_support": "Soporta muchos tipos de trabajos, incluyendo trabajos personalizados, tareas Spark, scripts Python, sincronización de datos, scripts Bash, importación Excel, llamadas de interfaz, trabajos SQL, scripts Curl, etc.", "real_work": "Programación flexible, arrastrar vertical y horizontalmente", "real_work_description": "Soporta todos los comandos del ciclo de vida del trabajo, incluyendo ejecutar, detener, interrumpir, desconectar, publicar, re-ejecutar todo, re-ejecutar downstream, re-ejecutar actual, llamada externa, transferencia de resultados, etc.", "multi_platform_deployment": "Mapa de metadatos, claro de un vistazo", "multi_platform_description": "Proporciona función de recolección de metadatos, ayuda a los usuarios a buscar rápidamente la distribución y estructura de datos, soporta acceso a múltiples fuentes de datos y visualización multidimensional.", "data_view": "Pantalla grande inteligente, visualización Big Data", "data_view_description": "Potente tecnología de procesamiento y visualización de datos, transforma datos complejos en gráficos intuitivos, soporta monitoreo y análisis en tiempo real, ayuda en la toma de decisiones empresariales, se adapta flexiblemente a las necesidades de la industria.", "opensource_value": "Ayudar a las empresas a utilizar capacidades Big Data", "free_trial": "<PERSON><PERSON><PERSON> gratuita", "zhi_yao_shu_ju": "Zhiyao Data", "build_enterprise_open_source_software": "Plataforma de computación Big Data ligera"}