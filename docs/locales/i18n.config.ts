import HomeEn from "./home/<USER>";
import HomeZh from "./home/<USER>";
import HomeJa from "./home/<USER>";
import HomeKo from "./home/<USER>";
import HomeFr from "./home/<USER>";
import HomeDe from "./home/<USER>";
import HomeEs from "./home/<USER>";
import HomeRu from "./home/<USER>";
import layoutEn from "./layout/en.json";
import layoutZh from "./layout/zh.json";
import layoutJa from "./layout/ja.json";
import layoutKo from "./layout/ko.json";
import layoutFr from "./layout/fr.json";
import layoutDe from "./layout/de.json";
import layoutEs from "./layout/es.json";
import layoutRu from "./layout/ru.json";

const en = {
  ...HomeEn,
  ...layoutEn,
};

const zh = {
  ...HomeZh,
  ...layoutZh,
};

const ja = {
  ...HomeJa,
  ...layoutJa,
};

const ko = {
  ...HomeKo,
  ...layoutKo,
};

const fr = {
  ...HomeFr,
  ...layoutFr,
};

const de = {
  ...HomeDe,
  ...layoutDe,
};

const es = {
  ...HomeEs,
  ...layoutEs,
};

const ru = {
  ...HomeRu,
  ...layoutRu,
};

export default defineI18nConfig(() => ({
  legacy: false,
  locale: "zh",
  messages: {
    en,
    zh,
    ja,
    ko,
    fr,
    de,
    es,
    ru,
  },
}));
