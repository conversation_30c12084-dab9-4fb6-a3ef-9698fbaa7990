---
title: "分享表单"
---

## 分享表单模块

> 通过拖拽组件的方式，用可视化的方式录入表的数据

#### 查询表单

> 查询表单列表
> 支持表单名称搜索

![20241219180316](https://img.isxcode.com/picgo/20241219180316.png)

> 表单操作，包括配置、编辑、删除、分享、发布、发布、下线

![20250109154206](https://img.isxcode.com/picgo/20250109154206.png)

#### 新建表单

> 点击`添加表单`按钮

![20241219180330](https://img.isxcode.com/picgo/20241219180330.png)

- **表单名称**：表单的名称
- **数据源**：指定需要操作数据的数据源
- **模式**：支持三种模式  
`自动创建表`：默认在指定数据源中创建以`SY_`开头的新表  
`选择已有表`：下拉，选择数据源中已存在的表  
`创建新表`：输入需要新建的表名，默认在数据库自动创建
- **备注**：非必填

#### 配置表单

> 点击`配置`按钮

![20250109154927](https://img.isxcode.com/picgo/20250109154927.png)

> 将左侧中的组件拖拽到画布中
> 组件支持绑定具体字段，也可绑定新增字段

![20241219180404](https://img.isxcode.com/picgo/20241219180404.png)

#### 查看数据

> 点击眼睛查看当前表单中已存在的数据

![20250109155004](https://img.isxcode.com/picgo/20250109155004.png)

![20241219180419](https://img.isxcode.com/picgo/20241219180419.png)

#### 数据的增删改查

> 点击`添加`按钮，新增一条数据

![20250109155117](https://img.isxcode.com/picgo/20250109155117.png)

> 点击`编辑`按钮，修改一条数据

![20250109155217](https://img.isxcode.com/picgo/20250109155217.png)

> 点击`删除`按钮，删除一条数据

![20250109155317](https://img.isxcode.com/picgo/20250109155317.png)

#### 分享表单

> 先发布表单，点击`分享`按钮

![20250109155420](https://img.isxcode.com/picgo/20250109155420.png)

> 指定生效时间，单位是天，默认1天

![20241219180454](https://img.isxcode.com/picgo/20241219180454.png)

> 生成分享链接并复制访问，跳转用户提交数据界面

![20241219180510](https://img.isxcode.com/picgo/20241219180510.png)

![20250109155616](https://img.isxcode.com/picgo/20250109155616.png)