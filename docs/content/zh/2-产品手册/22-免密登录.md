---
title: "免密登录"
---

## 免密登录模块

> 当前支持两种sso免密单点登录，包括Github、 Keycloak

Github文档：https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/authorizing-oauth-apps
Keycloak文档：https://www.keycloak.org/documentation

#### 添加单点配置

> 使用后台管理员`admin`登录系统，点击`免密登录`菜单

![20250324102958](https://img.isxcode.com/picgo/20250324102958.png)

> 点击`新增配置`按钮

![20250324103100](https://img.isxcode.com/picgo/20250324103100.png)

- **名称**: 必填，单点名称，在登录页面显示该名称的按钮
- **类型**: 必填，单点的类型
- **clientId**: 必填，单点的clientId，对应的认证平台获取
- **clientSecret**: 必填，单点的clientId，对应的认证平台获取
- **scope**: 非必填，单点参数，根据对应的平台推荐填写，如果是Keycloak类型，推荐填写openid
- **authUrl**: 必填，填写authorization_endpoint地址，认证平台的认证地址，获取code的地址
- **accessTokenUrl**: 必填，填写token_endpoint地址，通过code获取token的地址
- **redirectUrl**: 必填，填写 http://xxx/ssoauth?clientId=${clientId}  
注意！！！在最后一定要添加clientId对应的值，且认证平台配置的回调地址与该地址一定要保持一致    
例如： https://zhiqingyun-demo.isxcode.com/ssoauth?clientId=Ov23li3MrWzxpTXKCDbY
- **userUrl**: 必填，填写userinfo_endpoint地址，通过token获取用户信息地址
- **authJsonPath**: 必填，解析通过`userUrl请求`获取用户json信息，通过与`至轻云账号`进行匹配认证  
例如: 希望通过`phone`手机号认证用户，则填写`$.phone`
```json
{
  "username": "ispong",
  "phone": "13131341223"
}
```
- **备注**: 非必填

#### 一键复制跳转链接

![20250324105708](https://img.isxcode.com/picgo/20250324105708.png)

复制内容如下： https://github.com/login/oauth/authorize?client_id=Ov23li3MrWzxpTXKCDbY&redirect_uri=https://zhiqingyun-demo.isxcode.com/ssoauth?clientId=Ov23li3MrWzxpTXKCDbY  
通过复制的内容，可直接跳转进行免密登录

#### 免密登录

> 点击`登录页面`的`免密登录`按钮，可以跳转`单点登录页面`，支持多个单点登录方式

![20250324105208](https://img.isxcode.com/picgo/20250324105208.png)

#### Github配置参考

authUrl： https://github.com/login/oauth/authorize
accessTokenUrl：https://github.com/login/oauth/access_token
userUrl：https://api.github.com/user
redirectUrl：http://xxx/ssoauth?clientId=${clientId}  

#### Keycloak配置参考

> 通过平台获取对应的地址

![20250324110219](https://img.isxcode.com/picgo/20250324110219.png)

![20250324110410](https://img.isxcode.com/picgo/20250324110410.png)

authUrl： 搜素`authorization_endpoint`
accessTokenUrl：搜素`token_endpoint`
userUrl：搜素`userinfo_endpoint`
scope： openid
redirectUrl：http://xxx/ssoauth?clientId=${clientId}



