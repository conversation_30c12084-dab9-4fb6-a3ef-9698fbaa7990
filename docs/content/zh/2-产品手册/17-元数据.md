---
title: "元数据"
---

## 元数据模块

> 基于`资源管理`模块的`数据源`菜单中的数据源列表
> 对数据源中的表结构进行解析保存，便于用户进行查询搜索操作

数据地图分为三个维度：

- **数据源**：搜索所有创建过的数据源列表
- **表**：搜索所有数据源中的表信息
- **字段**：搜索所有表中的字段信息

#### 数据源查看

> 支持数据源、库名搜索

![20250109105226](https://img.isxcode.com/picgo/20250109105226.png)

- **数据源**：与数据源中的名称保持一一对应
- **库名**：数据源连接中连接的db库
- **类型**：数据源类型
- **状态**：`可用`表示数据源还未删除，`已删除`表示数据源已经被删除
- **备注**：可编辑，自定义备注
- **更新时间**：刷新数据源的更新时间

#### 刷新数据源

> 点击`刷新数据`按钮
> 需要用户手动执行刷新操作，来更新数据地图中的数据源信息

![20250109105936](https://img.isxcode.com/picgo/20250109105936.png)

#### 表查看

> 有两种方式跳转表查看

##### 通过点击`数据源名称`

> 通过点击`数据源名称`，可以查看当前数据源中所包含的表

![20250109110335](https://img.isxcode.com/picgo/20250109110335.png)

![20250109110359](https://img.isxcode.com/picgo/20250109110359.png)

##### 点击`表`过滤按钮

> 通过点击`表`过滤按钮，可查看所有数据源的中表

![20250109110626](https://img.isxcode.com/picgo/20250109110626.png)

#### 采集表数据

> 当用户新增了表，但数据地图中的表信息还未刷新
> 此时需要对数据源中的表信息，进行采集
> 点击`立即采集`

![20250109112429](https://img.isxcode.com/picgo/20250109112429.png)

- **采集任务名称**：采集任务的名称
- **数据源类型**：需要采集的数据源的类型
- **数据源**：指定需要采集的数据源
- **表**：表名规则，`所有表`指采集当前数据源中的所有表，`指定表`通过正则表达式指定需要采集的表  
举例：users_*，采集所有`users_`开头的表信息
- **备注**：非必填

#### 查看表详情

> 点击`表名`，查看表的具体详情

![20250109112916](https://img.isxcode.com/picgo/20250109112916.png)

##### 表基础信息

- `表名`：表名
- `字段数据量`：表中包含的字段个数
- `表总条数`：表中包含的数据条数
- `表大小`：表物理存储大小
- `采集时间`：表的采集时间
- `刷新时间`：表的基础信息刷新时间
- `备注`：表的备注

##### 刷新表信息

> 点击刷新按钮
> 通过点击刷新按钮，获取当前表的最新数据

![20250109113226](https://img.isxcode.com/picgo/20250109113226.png)

##### 字段信息

> 查看当前表中的字段信息
> 包括字段名、字段类型、是否为分区、备注

![20250109113321](https://img.isxcode.com/picgo/20250109113321.png)

##### 数据预览

> 预览表中的数据，最多200条

![20250109113502](https://img.isxcode.com/picgo/20250109113502.png)

##### 数据导出

> 导出表中的数据，导出Excel格式，最大条数10000条

![20250109113642](https://img.isxcode.com/picgo/20250109113642.png)

#### 查看字段列表

> 支持库名、表名、备注全局搜索

![20250109113816](https://img.isxcode.com/picgo/20250109113816.png)

### 元数据采集

> 创建定时任务，定时调度采集数据地图中的元数据
> 参考表立即采集
> 可配置开启调度，并指定调度的时间，支持cron表达式

![20250109113923](https://img.isxcode.com/picgo/20250109113923.png)

定时采集任务也可以立即采集

![20250109114256](https://img.isxcode.com/picgo/20250109114256.png)

### 采集实例

> 查看元数据采集任务，是否成功，以及失败日志

![20250109114214](https://img.isxcode.com/picgo/20250109114214.png)