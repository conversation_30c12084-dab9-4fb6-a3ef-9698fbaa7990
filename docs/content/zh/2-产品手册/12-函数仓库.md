---
title: "函数仓库"
---

#### 查询函数仓库

> 自定义函数参考: https://github.com/isxcode/spark-function-template

![20241220162142](https://img.isxcode.com/picgo/20241220162142.png)

#### 添加函数

> 点击`添加函数`按钮

- **名称**: 必填，该名称，将在sql中的当函数名称使用，租户内名称唯一，推荐全字母小写 
- **类型**: 必填，支持[Scalar User-Defined Functions (UDF)](https://spark.apache.org/docs/latest/sql-ref-functions-udf-scalar.html)、[User-Defined Aggregate Functions (UDAF)](https://spark.apache.org/docs/latest/sql-ref-functions-udf-aggregate.html) 
- **资源文件**: 必填， `资源中心`中的`函数`类型的文件
- **类名**: 必填， 自定义函数的入口，例如: com.isxcode.spark.udf.Func 
- **结果类型**: 必填， 包括 string、int、long、double、boolean、date、timestamp
- **备注**: 非必填

![20240518113710](https://img.isxcode.com/picgo/20240518113710.png)

![20240518113725](https://img.isxcode.com/picgo/20240518113725.png)

![20240518113737](https://img.isxcode.com/picgo/20240518113737.png)

![20240518113802](https://img.isxcode.com/picgo/20240518113802.png)

> 支持在`SparkSql查询作业`中使用
> 注意！！！ 如果自定义函数需要依赖，在使用过程中，需要手动添加`依赖配置`

![20240518113851](https://img.isxcode.com/picgo/20240518113851.png)

> 支持在`数据同步`字段转换中使用

![20240518113918](https://img.isxcode.com/picgo/20240518113918.png)

> 支持在`实时计算`字段转换中使用

![20241220162604](https://img.isxcode.com/picgo/20241220162604.png)

> 支持在`Excel导入作业`字段转换中使用

![20241220162701](https://img.isxcode.com/picgo/20241220162701.png)