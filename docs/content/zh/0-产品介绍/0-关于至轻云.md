---
title:  '关于至轻云'
---

### 产品介绍

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 至轻云是一款企业级、智能化大数据中心。一键部署，开箱即用。可快速实现大数据计算、数据采集、数据清洗、数据安全、数据质量、数据管理、数据接口开放等功能，助力企业构建新一代智慧数据中心。

### 演示环境

演示地址: https://zhiqingyun-demo.isxcode.com
体验账号: `zhiyao`
账号密码: `zhiyao123`

### 产品优势

#### 开源轻量化

- **免中间件安装**: 开箱即用，无需安装额外中间件服务。
- **云原生架构**: 支持Docker、Rancher平台的快速部署。
- **国内镜像下载**: 可直接从阿里云镜像仓库进行安装，无需手动导入镜像。
- **代码自研**: 系统代码自研程度高，避免对外部服务的依赖。
- **运维成本低**: 轻量级产品，无需大量运维人员。

#### 高性价比资源使用

- **超低资源配置**: 平台的最低配置要求仅为2核2GB。
- **集群优化**: 计算集群最低仅需4核8GB即可实现作业运行体验。

#### 支持信创

- **国产系统兼容**: 全面支持国产麒麟V10操作系统，适应国家对信息技术的信创政策要求。
- **多架构高兼容性**: 系统兼容AMD和ARM架构，能够适配多种硬件平台。

#### 复杂数据开发

- **原生Spark/Flink**: 支持SparkSQL/FlinkSQL语法及官方的默认函数库。
- **跨库计算**: 支持跨数据源计算，允许用户在不同数据源之间进行灵活的数据整合。
- **自定义函数**: 支持使用Java等语言编写自定义函数，如国密算法和对称加解密等。
- **自定义作业**: 支持用户编写Java作业，运行Jar包，能够执行复杂业务的计算。
- **多类型数据源**: 支持处理结构化和非结构化数据，适应多样化的数据场景。

#### 大数据计算

- **高效数据采集**: 支持千万级别数据在数分钟内完成数据同步，能够轻松应对亿级数据量的计算需求。
- **分布式计算**: 支持任务拆分、数据拆分，实现调用集群能力，完成分布式数据计算。

#### 系统安全性高

- **假删除保护**: 全平台采用假删除机制，确保数据不被实际删除，提供数据可追溯性。
- **用户行为追踪**: 支持全平台用户行为跟踪记录，实时监控用户操作，提升系统的安全监控。
- **单一接口暴露**: 仅暴露一个对外端口，降低系统的攻击面，增强整体安全性，防止潜在的安全漏洞。

#### 多租户模式

- **租户独立管理**: 系统支持租户拆分，允许用户灵活配置不同租户的运行环境。
- **数据隔离保障**: 支持租户间的数据隔离，确保每个租户的数据安全与隐私保护。