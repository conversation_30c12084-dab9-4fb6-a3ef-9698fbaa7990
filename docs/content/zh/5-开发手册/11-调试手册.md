---
title: "调试手册"
---

#### 查看至轻云日志

```bash
cd /opt/zhiqingyun/logs
tail -f spark-yun.log
```

#### 查看至轻云代理日志

```bash
cd ~/zhiqingyun-agent/logs/
tail -f zhiqingyun-agent.log
```

#### 在线设置日志级别

http://localhost:8080/tools/open/setLogLevel?level=DEBUG&name=admin&password=admin123

> 日志级别有`TRACE`、`DEBUG`、`INFO`、`WARN`、`ERROR`、`FATAL`

![20241212182756](https://img.isxcode.com/picgo/20241212182756.png)

#### 关闭flyway自动运行sql脚本

```wikitext
--spring.flyway.enabled=false
```

![20250110141816](https://img.isxcode.com/picgo/20250110141816.png)

#### 手动设置日志级别

```wikitext
--logging.level.root=info
```