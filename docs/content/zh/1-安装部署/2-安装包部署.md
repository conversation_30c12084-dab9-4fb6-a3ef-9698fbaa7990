---
title: "安装包部署"
---

## 使用官网安装包部署

#### 下载安装包

官网链接：https://zhiqingyun.isxcode.com/

![20240427182151](https://img.isxcode.com/picgo/20240427182151.png)

#### 下载解压安装包

```bash
cd /tmp
nohup wget https://zhiqingyun-demo.isxcode.com/tools/open/file/zhiqingyun.tar.gz >> download_zhiqingyun.log 2>&1 &
tail -f download_zhiqingyun.log
tar -vzxf zhiqingyun.tar.gz
```

#### 离线安装java环境

```bash
cd /tmp
nohup wget https://openfly.oss-cn-shanghai.aliyuncs.com/java/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz >> download_jdk.log 2>&1 &
tail -f download_jdk.log
```

> 注意！！！一定要在~/.bashrc中配置JAVA_HOME环境变量

```bash
tar -vzxf /tmp/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz -C /opt
ln -s /opt/zulu8.78.0.19-ca-jdk8.0.412-linux_x64 /opt/java
tee -a ~/.bashrc <<-'EOF'
export JAVA_HOME=/opt/java
export PATH=$PATH:$JAVA_HOME/bin
EOF
source ~/.bashrc
java -version
```

#### 修改管理员登陆密码

```bash
vim zhiqingyun/conf/application-local.yml
```

```yml
isx-app:
  admin-passwd: admin1234
```

#### 启动项目

```bash
bash zhiqingyun/bin/start.sh
```

#### 访问项目

- 访问项目: http://localhost:8080 
- 管理员账号：`admin` 
- 管理员密码：`admin1234` 


#### 配置至轻云开机自启（可选）

```bash
chmod a+x /opt/zhiqingyun/bin/start.sh
chmod a+x /opt/zhiqingyun/bin/stop.sh
```

#### 配置自启文件

```bash
vim /usr/lib/systemd/system/zhiqingyun.service
```

> 配置启动脚本路径和pid文件路径和启动用户

```bash
[Unit]
Description=Zhiqingyun Service unit Configuration
After=network.target

[Service]
Type=forking

ExecStart=/opt/zhiqingyun/bin/start.sh --print-log="false"
ExecStop=/opt/zhiqingyun/bin/stop.sh
ExecReload=/opt/zhiqingyun/bin/start.sh --print-log="false"
PIDFile=/opt/zhiqingyun/zhiqingyun.pid
KillMode=none
Restart=always
User=root
Group=root

[Install]
WantedBy=multi-user.target
```

#### 重载服务

```bash
systemctl daemon-reload
```

#### 设置开机自启

```bash
systemctl enable zhiqingyun
```

#### 相关操作命令

```bash
# 启动至轻云
systemctl start zhiqingyun

# 查看至轻云状态
systemctl status zhiqingyun

# 停止至轻云
systemctl stop zhiqingyun

# 重启至轻云
systemctl restart zhiqingyun
```