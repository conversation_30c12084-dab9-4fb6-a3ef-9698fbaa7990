---
title: "前端独立部署"
---

## 前端独立部署

> 至轻云支持前后端分离部署

#### 1. 本地安装Nginx

[参考Nginx官方安装手册](https://nginx.org/en/docs/install.html)

#### 2. 修改Nginx配置文件并启动Nginx

```bash
vim nginx/conf.d/default.conf
```

> 自定义配置root路径：`/user/ispong/share/nginx/html`

```wikitext
server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;
   
    location / {
        root   /user/ispong/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /user/ispong/share/nginx/html;
    }
}
```

#### 3. 下载源码

```bash
git clone https://github.com/isxcode/spark-yun.git
```

#### 4. 修改前端访问地址

> 修改`.env`文件

```bash
cd spark-yun/spark-yun-frontend
vim spark-yun/spark-yun-frontend/.env
```

> 将${backend_url}替换成后端访问地址，例如 https://zhiqingyun-demo.isxcode.com

```wikitext
VITE_VUE_APP_BASE_DOMAIN=${backend_url}
```

![20250428184230](https://img.isxcode.com/picgo/20250428184230.png)

#### 5. 前端源码本地打包

> 前端包输出路径：spark-yun/spark-yun-frontend/dist

```bash
cd spark-yun/spark-yun-frontend
npm install pnpm@9.0.6 -g
pnpm install --force
pnpm run build
```

![20250428185056](https://img.isxcode.com/picgo/20250428185056.png)

#### 6. 上传dist文件夹

> 上传dist目录的文件到nginx的html目录下

```bash
cp -rf dist/* /user/ispong/share/nginx/html
```

#### 7. 刷新Nginx并访问系统

```bash
nginx -s reload
```

- 访问地址: http://localhost:80
- 管理员账号：`admin` 
- 管理员密码：`admin123`