---
title: "资源中心"
---

#### 问题1: 上传文件到资源中心，接口报错413

```log
Request failed with status code 413
```

**解决方案**

```wikitext
出现这种错误，一般是Nginx配置错误导致的，添加以下配置
限制了请求体的大小，将client_max_body_size设置大一些
```

```wikitext
http {
  client_max_body_size 150M;
}
```

#### 问题2: 上传文件到资源中心，后台报错

```log
Caused by: java.lang.IllegalStateException: org.apache.tomcat.util.http.fileupload.impl.SizeLimitExceededException: the request was rejected because its size (786669567) exceeds the configured maximum (104857600)
	at org.apache.catalina.connector.Request.parseParts(Request.java:2982)
	at org.apache.catalina.connector.Request.getParts(Request.java:2834)
	at org.apache.catalina.connector.RequestFacade.getParts(RequestFacade.java:1098)
	at javax.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:361)
	at javax.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:361)
	at javax.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:361)
	at javax.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:361)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.parseRequest(StandardMultipartHttpServletRequest.java:95)
```

**解决方案**

```wikitext
出现这种错误，是Spring配置文件传输上限配置太低了，修改配置文件内容如下
```

```yml
spring:
  servlet:
    multipart:
      max-file-size: 100GB
      max-request-size: 100GB
      enabled: true
```
