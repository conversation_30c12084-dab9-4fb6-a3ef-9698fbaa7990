---
title: "源码打包"
---

#### 问题1: 官网打包编译命令无法下载gradle

**解决方案**

```wikitext
手动下载gradle安装包，解压到gradle默认路径 `${user_home_dir}/.gradle/wrapper/dists` 下
```

Gradle7.6.1下载地址：https://zhiqingyun-demo.isxcode.com/tools/open/file/gradle-7.6.1-bin.zip

```bash
unzip gradle-7.6.1-bin.zip
cp gradle-7.6.1 ~/.gradle/wrapper/dists/gradle-7.6.1-bin
cd ~/spark-yun
./gradlew install clean package start
```

![20241022103809](https://img.isxcode.com/picgo/20241022103809.png)

#### 问题2: idea无法启动项目

报错如下

![20250422142959](https://img.isxcode.com/picgo/20250422142959.png)

**解决方案**

修改启动项配置

![20250422143104](https://img.isxcode.com/picgo/20250422143104.png)

![20250422143028](https://img.isxcode.com/picgo/20250422143028.png)

#### 问题3: git bash 打包乱码

> 设置git bash的字符集

![20250423155751](https://img.isxcode.com/picgo/20250423155751.png)

```bash
./gradlew.bat install -Dfile.encoding=UTF-8
```