---
title: "许可证"
---

#### 问题1: 麒麟V10系统，许可证上传报错

```log
Caused by: java.io.IOException: ObjectIdentifier() -- data isn't an object ID (tag = 48)
        at sun.security.util.ObjectIdentifier.<init>(ObjectIdentifier.java:285) ~[na:1.8.0_272]
        at sun.security.util.DerInputStream.getOID(DerInputStream.java:320) ~[na:1.8.0_272]
        at com.sun.crypto.provider.PBES2Parameters.engineInit(PBES2Parameters.java:267) ~[sunjce_provider.jar:1.8.0_272]
        at java.security.AlgorithmParameters.init(AlgorithmParameters.java:293) ~[na:1.8.0_272]
        at global.namespace.truelicense.v4.V4Encryption.param(V4Encryption.java:64) ~[truelicense-v4-4.0.3.jar!/:na]
        at global.namespace.truelicense.v4.V4Encryption.lambda$input$1(V4Encryption.java:52) ~[truelicense-v4-4.0.3.jar!/:na]
        at global.namespace.fun.io.api.Socket.lambda$map$0(Socket.java:138) ~[fun-io-api-2.3.0.jar!/:2.3.0]
        at global.namespace.fun.io.api.Socket.lambda$map$0(Socket.java:136) ~[fun-io-api-2.3.0.jar!/:2.3.0]
        at global.namespace.fun.io.api.Socket.lambda$map$0(Socket.java:136) ~[fun-io-api-2.3.0.jar!/:2.3.0]
        at global.namespace.fun.io.api.Socket.lambda$map$0(Socket.java:136) ~[fun-io-api-2.3.0.jar!/:2.3.0]
        at global.namespace.truelicense.core.Filters$1.lambda$input$1(Filters.java:51) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.fun.io.api.Socket.apply(Socket.java:123) ~[fun-io-api-2.3.0.jar!/:2.3.0]
        at global.namespace.fun.io.jackson.JsonCodec$1.decode(JsonCodec.java:44) ~[fun-io-jackson-2.3.0.jar!/:2.3.0]
        at global.namespace.truelicense.core.TrueLicenseManagementContext$TrueLicenseManagerParameters$TrueLicenseManager.repositoryModel(TrueLicenseManagementContext.java:810) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.truelicense.core.TrueLicenseManagementContext$TrueLicenseManagerParameters$TrueLicenseManager.repositoryController(TrueLicenseManagementContext.java:804) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.truelicense.core.TrueLicenseManagementContext$TrueLicenseManagerParameters$TrueLicenseManager.authenticate(TrueLicenseManagementContext.java:800) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.truelicense.core.TrueLicenseManagementContext$TrueLicenseManagerParameters$CachingLicenseManager.authenticate(TrueLicenseManagementContext.java:673) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.truelicense.core.TrueLicenseManagementContext$TrueLicenseManagerParameters$TrueLicenseManager.decodeLicense(TrueLicenseManagementContext.java:796) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.truelicense.core.TrueLicenseManagementContext$TrueLicenseManagerParameters$TrueLicenseManager.lambda$install$1(TrueLicenseManagementContext.java:750) ~[truelicense-core-4.0.3.jar!/:na]
        at global.namespace.truelicense.core.TrueLicenseManagementContext.callChecked(TrueLicenseManagementContext.java:79) ~[truelicense-core-4.0.3.jar!/:na]
```

**解决方案**

```wikitext
麒麟系统本地默认的java版本不匹配，重新指定一个JAVA_HOME，推荐使用java1.8
```

```bash
vim /opt/zhiqingyun/conf/zhiqingyun-env.sh
```

```bash
export JAVA_HOME=/zulu/java
```