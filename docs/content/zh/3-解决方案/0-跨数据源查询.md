---
title: "跨数据源查询"
---

### 案例说明

> 跨越多个数据源的数据进行sql分析计算

### 案例实现

> 创建`SparkSql查询作业`

![20241212161042](https://img.isxcode.com/picgo/20241212161042.png)

##### Sql如下

> 聚合mysql和postgresql中的数据

```sql
-- mysql中数据
CREATE database db1;
USE db1;
CREATE TABLE table1
USING JDBC
OPTIONS (
    driver 'com.mysql.cj.jdbc.Driver',
    url '**************************************',
    user 'ispong',
    password 'ispong123',
    dbtable 'zqy_users_jdbc'
);

-- postgresql中数据
CREATE database db2;
USE db2;
CREATE TABLE table2
USING JDBC
OPTIONS (
    driver 'org.postgresql.Driver',
    url '********************************************',
    user 'postgres',
    password 'ispong123',
    dbtable 'users'
);

-- 执行计算sql
select * from db1.table1 union all select * from db2.table2;
```

![20241212162011](https://img.isxcode.com/picgo/20241212162011.png)

![20241212162028](https://img.isxcode.com/picgo/20241212162028.png)

![20241212161936](https://img.isxcode.com/picgo/20241212161936.png)